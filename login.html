<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Login</title>
    <link rel="stylesheet" href="src/css/style.css">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GS1GYSFBPX"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-GS1GYSFBPX');
    </script>

    <style>
        .login-container {
            max-width: 350px;
            margin: 50px auto;
            border: 1px solid #000;
            padding: 15px;
        }

        .login-header {
            padding: 4px 8px;
            margin: -15px -15px 15px -15px;
            font-weight: bold;
        }

        .auth-tabs {
            margin-bottom: 15px;
        }

        .auth-tab-btn {
            margin-right: 5px;
        }

        .auth-tab-btn.active {
            background: #000;
            color: white;
        }

        .auth-form {
            border: 1px solid #000;
            padding: 10px;
            margin-bottom: 15px;
            display: block;
        }

        .auth-form input {
            width: 100%;
            padding: 2px;
            border: 1px solid #000;
            margin-bottom: 8px;
            display: block;
            box-sizing: border-box;
        }

        .loading {
            display: none;
        }

        .error {
            display: none;
            color: #800000;
            background: #ffcccc;
            border: 1px solid #800000;
            padding: 5px;
        }

        .success {
            display: none;
            color: #008000;
            background: #ccffcc;
            border: 1px solid #008000;
            padding: 5px;
        }

        .divider {
            text-align: center;
            margin: 15px 0;
            border-bottom: 1px solid #000;
            height: 10px;
        }

        .divider span {
            background: white;
            padding: 0 10px;
        }

        #already-logged-in h3 {
            margin-top: 0;
            color: #333;
        }

        #already-logged-in p {
            color: #666;
            margin: 10px 0;
        }

        .btn {
            padding: 10px 20px;
            border: 2px solid #007bff;
            background: #007bff;
            color: white;
            cursor: pointer;
            border-radius: 5px;
            font-size: 14px;
            margin: 5px;
        }

        .btn:hover {
            background: #0056b3;
            border-color: #0056b3;
        }

        .home-btn-container {
            text-align: center;
            margin: 20px 0;
        }

        .home-btn {
            display: inline-block;
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #ddd;
            color: #333;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
            transition: all 0.2s;
        }

        .home-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-header">
            Fish Login
        </div>

        <div class="auth-tabs">
            <button type="button" class="auth-tab-btn active" onclick="showAuthForm('signin')">Sign In</button>
            <button type="button" class="auth-tab-btn" onclick="showAuthForm('signup')">Sign Up</button>
        </div>

        <!-- Sign In Form -->
        <form id="signin-form" class="auth-form">
            <strong>Email:</strong><br>
            <input type="email" id="signin-email" required><br>
            <strong>Password:</strong><br>
            <input type="password" id="signin-password" required><br><br>
            <button type="submit">Login</button>
            <br><br>
            <a href="#" id="forgot-password-link" onclick="showForgotPasswordForm()">Forgot Password?</a>
        </form>

        <!-- Sign Up Form -->
        <form id="signup-form" class="auth-form" style="display: none;">
            <strong>Email:</strong><br>
            <input type="email" id="signup-email" required><br>
            <strong>Password:</strong><br>
            <input type="password" id="signup-password" required><br><br>
            <button type="submit">Register</button> 
        </form>

        <!-- Forgot Password Form -->
        <form id="forgot-password-form" class="auth-form" style="display: none;">
            <strong>Enter your email address:</strong><br>
            <input type="email" id="forgot-email" required><br><br>
            <button type="submit">Send Reset Email</button>
            <br><br>
            <a href="#" onclick="showAuthForm('signin')">Back to Sign In</a>
        </form>

        <div class="divider">
            <span>or</span>
        </div>

        <div id="g_id_signin"></div>

        <div id="loading" class="loading">
            Verifying credentials...
        </div>

        <div id="error" class="error">
            Authentication failed. Please try again.
        </div>

        <div id="success" class="success">
            Password reset email sent! Please check your inbox.
        </div>

        <!-- Already logged in section -->
        <div id="already-logged-in" class="auth-form" style="display: none; text-align: center;">
            <h3>You are already logged in!</h3>
            <p id="logged-in-user-info"></p>
            <button type="button" class="btn" onclick="goToTanks()">Go to My Tanks</button>
            <br><br>
            <button type="button" class="btn" onclick="logoutAndStay()" style="background: #dc3545; border-color: #dc3545;">Logout</button>
        </div>
    </div>

    <div class="home-btn-container">
        <a href="index.html" class="home-btn">Back To Draw</a>
    </div>

    <!-- Load Google's auth2 -->
    <script src="src/js/footer-utils.js"></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script src="src/js/fish-utils.js"></script>
    <script src="src/js/firebase-init.js"></script>
    <script src="src/js/login.js"></script>
</body>

</html>