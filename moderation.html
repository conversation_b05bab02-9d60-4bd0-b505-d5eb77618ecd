<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Moderation Panel</title>
    <link rel="stylesheet" href="src/css/style.css">
    <link rel="stylesheet" href="src/css/moderation.css">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GS1GYSFBPX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-GS1GYSFBPX');
    </script>
</head>
<body>
    <div class="moderation-header">
        <h1>Fish Moderation Panel</h1>
        <div>
            Review and moderate user-submitted fish drawings
            <button class="logout-btn" onclick="logout()" style="float: right;">Logout</button>
        </div>
    </div>
        
        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number" id="totalFish">-</div>
                <div>Total Fish</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="flaggedFish">-</div>
                <div>Flagged</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="approvedFish">-</div>
                <div>Approved</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="deletedFish">-</div>
                <div>Deleted</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="pendingFish">-</div>
                <div>Pending</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="validFish">-</div>
                <div>Valid Fish</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="invalidFish">-</div>
                <div>Invalid Fish</div>
            </div>
        </div>
        
        <div class="filters control-panel">
            <strong>Filters:</strong>
            <button class="filter-btn active" onclick="setFilter('all')">All Fish</button>
            <button class="filter-btn" onclick="setFilter('flagged')">Flagged</button>
            <button class="filter-btn" onclick="setFilter('reported')">Reported</button>
            <button class="filter-btn" onclick="setFilter('recent')">Recent (24h)</button>
            <button class="filter-btn" onclick="setFilter('high-score')">High Score</button>
            <button class="filter-btn" onclick="setFilter('low-score')">Low Score</button>
            <button class="filter-btn" onclick="setFilter('deleted')">Deleted</button>
            <button class="filter-btn" onclick="setFilter('needs-validity')">Needs Validity Review</button>
            <button class="filter-btn" onclick="setFilter('valid')">Valid Fish</button>
            <button class="filter-btn" onclick="setFilter('invalid')">Invalid Fish</button>
        </div>
        
        <div class="swipe-moderation-section control-panel" style="margin-top: 10px;">
            <strong>Quick Moderation:</strong>
            <button class="action-btn" onclick="window.location.href='swipe-moderation.html'" style="background: #FF6B35; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                📱 Swipe to Moderate Flagged Fish
            </button>
            <span style="margin-left: 10px; font-style: italic; color: #666;">Tinder-like interface for quick moderation</span>
        </div>
        
        <div class="download-section control-panel" style="margin-top: 10px;">
            <strong>Training Data:</strong>
            <button class="action-btn" onclick="downloadAllImages()" id="downloadBtn" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                📥 Download All Images (Including Deleted)
            </button>
            <span id="downloadStatus" style="margin-left: 10px; font-style: italic; color: #666;"></span>
        </div>
        
        <div class="selection-controls control-panel" style="margin-bottom: 10px;">
            <strong>Selection:</strong>
            <button class="filter-btn" onclick="selectAll()">Select All</button>
            <button class="filter-btn" onclick="selectNone()">Select None</button>
            <span style="margin-left: 10px; font-size: 12px; color: #666;">
                💡 Tip: Hold Shift and click checkboxes to select ranges. Press Ctrl+A to select all.
            </span>
        </div>
        
        <div class="bulk-actions" style="margin-bottom: 20px; display: none;" id="bulkActions">
            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                <span id="selectedCount">0 selected</span>
                <button class="action-btn approve-btn" onclick="bulkApprove()">Bulk Approve</button>
                <button class="action-btn delete-btn" onclick="bulkDelete()">Bulk Delete</button>
                <button class="action-btn" onclick="bulkMarkAsFish()" style="background: #2196F3; color: white;">Mark as Fish</button>
                <button class="action-btn" onclick="bulkMarkAsNotFish()" style="background: #FF9800; color: white;">Mark as Not Fish</button>
                <button class="action-btn" onclick="bulkClearReports()" style="background: #ff9800; color: white;">🧹 Clear Reports</button>
                <button class="action-btn ban-btn" onclick="bulkBanUsers()" style="background: #dc3545; color: white;">Ban Users</button>
                <button class="filter-btn" onclick="clearSelection()">Clear Selection</button>
            </div>
        </div>
        
        <div id="loading" class="loading">Loading fish for moderation...</div>
        <div id="fishGrid" class="fish-grid" style="display: none;"></div>
        
        <div id="loadMore" style="text-align: center; margin-top: 20px; display: none;">
            <button onclick="loadMoreFish()" style="padding: 10px 20px; background: #0288d1; color: white; border: none; border-radius: 5px; cursor: pointer;">
                Load More Fish
            </button>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="src/js/footer-utils.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    
    <!-- JSZip library for creating ZIP files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    
    <script src="src/js/firebase-init.js"></script>
    <script src="src/js/fish-utils.js"></script>
    <script src="src/js/moderation.js"></script>
</body>
</html>