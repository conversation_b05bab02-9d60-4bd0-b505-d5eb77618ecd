<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Tank - Watch Community Fish Swim | DrawAFish.com</title>
    <meta name="description" content="Watch all the community-created fish swim together in our interactive fish tank. View the most recent, popular, or random fish drawings from our creative community.">
    <meta name="keywords" content="fish tank, swimming fish, community art, fish animation, digital aquarium, fish gallery">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Fish Tank - Watch Community Fish Swim">
    <meta property="og:description" content="Watch all the community-created fish swim together in our interactive fish tank. View recent, popular, or random fish drawings.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://drawafish.com/tank.html">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://drawafish.com/tank.html">
    
    <link rel="stylesheet" href="src/css/style.css">
    <link rel="icon" href="public/favicon.ico">
    
    <style>
        /* Modal styling - matching rank page design */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 100px auto;
            padding: 20px;
            width: 400px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        /* Special styling for add-to-tank modal */
        .modal-content.wide {
            width: 600px;
            max-width: 90vw;
        }
        
        .close {
            float: right;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }
        
        .close:hover {
            color: #333;
        }
        
        /* Fish info modal specific styles */
        .fish-info-modal {
            text-align: center;
        }
        
        .fish-info-modal h3 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
        }
        
        .fish-info-modal .fish-details {
            margin: 15px 0;
            color: #666;
            font-size: 14px;
        }
        
        .fish-info-modal .fish-score {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        
        /* Voting controls styling */
        .voting-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            align-items: center;
            margin-top: 15px;
        }
        
        .vote-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.2s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .vote-btn:hover {
            background: #f0f0f0;
            transform: translateY(-1px);
        }
        
        .upvote-btn:hover {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .downvote-btn:hover {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .report-btn {
            padding: 8px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.2s ease;
        }
        
        .report-btn:hover {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .vote-count {
            font-weight: bold;
        }
        
        /* Tank selection in modal */
        .tank-selection-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            max-height: 400px;
            overflow-y: auto;
            padding: 5px;
        }
        
        .tank-selection-item {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 16px;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .tank-selection-item:hover {
            border-color: #007bff;
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }
        
        .tank-selection-item h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .tank-selection-item .tank-description {
            margin: 0 0 8px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .tank-selection-stats {
            display: flex;
            gap: 15px;
            margin: 8px 0 12px 0;
            font-size: 13px;
            color: #666;
        }
        
        .tank-selection-stats .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .tank-privacy-indicator {
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .tank-privacy-indicator.public {
            background: #d4edda;
            color: #155724;
        }
        
        .tank-privacy-indicator.private {
            background: #f8d7da;
            color: #721c24;
        }
        
        .add-to-tank-btn {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .add-to-tank-btn:hover {
            background: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
        }
        
        .no-tanks-message {
            text-align: center;
            padding: 30px 20px;
            border: 2px dashed #ddd;
            border-radius: 12px;
            background: #f8f9fa;
            color: #666;
        }
        
        .no-tanks-message h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.2em;
        }
        
        .no-tanks-message p {
            margin: 0 0 15px 0;
            line-height: 1.5;
        }
        
        .no-tanks-message .btn {
            display: inline-block;
            text-decoration: none;
            padding: 10px 20px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 5px;
            transition: all 0.2s ease;
        }
        
        .no-tanks-message .btn:hover {
            background: #007bff;
            color: white;
            transform: translateY(-2px);
        }
        
        /* Mobile-specific styles */
        @media (max-width: 768px) {
            .modal-content {
                width: 90%;
                margin: 50px auto;
            }
            
            body {
                margin: 0;
                padding: 0;
                touch-action: manipulation; /* Prevent zoom on double-tap */
                display: flex;
                flex-direction: column;
                height: 100vh;
            }
            
            #swim-canvas {
                width: 100vw !important;
                height: calc(100vh - 120px) !important;
                max-width: 100vw !important;
                border: none !important;
                display: block !important;
                margin: 0 !important;
                flex: 1;
                touch-action: manipulation;
                position: relative;
                z-index: 1;
            }
            
            .controls-container {
                position: static !important;
                transform: none !important;
                background: rgba(255, 255, 255, 0.95);
                padding: 8px;
                border-top: 1px solid #ddd;
                font-size: 11px;
                text-align: center;
                backdrop-filter: blur(10px);
                flex-shrink: 0;
            }
            
            .controls-container select,
            .controls-container button,
            .controls-container input[type="range"] {
                margin: 2px;
                font-size: 12px;
            }
            
            .desktop-only {
                display: none;
            }
            
            .mobile-only {
                display: inline;
            }
            
            footer {
                position: static !important;
                background: rgba(255, 255, 255, 0.95);
                padding: 8px 5px;
                font-size: 10px;
                backdrop-filter: blur(10px);
                border-top: 1px solid #ddd;
                flex-shrink: 0;
                margin: 0;
            }
        }
        
        @media (min-width: 769px) {
            .desktop-only {
                display: inline;
            }
            
            .mobile-only {
                display: none;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
            
            #swim-canvas {
                width: 100vw !important;
                height: 100vh !important;
                max-width: 100vw !important;
                border: none !important;
                display: block !important;
                margin: 0 !important;
            }
            
            .controls-container {
                position: static;
                transform: none;
                background: rgba(255, 255, 255, 0.95);
                padding: 15px;
                border-top: 1px solid #ddd;
                backdrop-filter: blur(10px);
                text-align: center;
                margin: 0;
            }
        }
        
        /* Responsive design for tank selection modal */
        @media (max-width: 768px) {
            .modal-content.wide {
                width: 95% !important;
                margin: 50px auto;
            }
            
            .tank-selection-grid {
                max-height: 300px;
                gap: 10px;
            }
            
            .tank-selection-item {
                padding: 12px;
            }
            
            .tank-selection-item h4 {
                font-size: 1em;
            }
            
            .tank-selection-stats {
                font-size: 12px;
                gap: 12px;
            }
            
            .add-to-tank-btn {
                padding: 8px 12px;
                font-size: 13px;
            }
        }
    </style>
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GS1GYSFBPX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-GS1GYSFBPX');
    </script>
</head>
<body>
  <canvas id="swim-canvas" width="800" height="400" style="border:1px solid #888; background:#e0f7fa; display:block; margin: 0 auto 0 auto;"></canvas>
  
  <div class="controls-container" style="text-align: center; margin-top: 15px; font-size: 12px; color: #666;">
    View: 
    <select id="tank-sort" style="font-size: 11px; margin: 0 5px;">
      <option value="recent">Most Recent</option>
      <option value="popular">Most Popular</option>
      <option value="random">Random</option>
    </select>
    <button id="refresh-tank" style="font-size: 11px; margin-left: 5px;">refresh</button>
    | 
    <label style="font-size: 11px; margin-left: 5px;">
      <input type="checkbox" id="notifications-toggle" checked style="margin-right: 3px;">
      notifications
    </label>
    | 
    <a href="rank.html" style="color: #0066cc; text-decoration: underline; font-size: 11px;">rankings</a>
    | 
    <a href="profile.html" style="color: #0066cc; text-decoration: underline; font-size: 11px;">profile</a>
    | 
    <a href="fishtanks.html" id="my-tanks-link" style="color: #0066cc; text-decoration: underline; font-size: 11px;">my tanks</a>
    
    <div style="margin-top: 8px;">
      <label style="font-size: 11px; color: #666;">
        Fish count: <span id="fish-count-display" style="font-weight: 500;">50</span>
        <input type="range" id="fish-count-slider" min="1" max="100" value="50" 
               style="width: 120px; margin: 0 8px; vertical-align: middle; cursor: pointer;">
        <span id="current-fish-count" style="font-size: 10px; color: #999;"></span>
      </label>
    </div>
    
    <div style="margin-top: 8px; font-size: 11px; color: #666;">
      <strong>Feed the fish:</strong> 
      <span class="desktop-only">Hold Shift + Click or Right-click</span>
      <span class="mobile-only">Double-tap or Long-press</span>
      to drop food pellets
    </div>
    
    <div id="loading-indicator" style="display: none; font-size: 11px; color: #999; margin-top: 5px;">
      loading...
    </div>
  </div>
  <script src="src/js/footer-utils.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
  <script src="src/js/firebase-init.js"></script>
  <script src="src/js/fish-utils.js"></script>
  <script src="src/js/modal-utils.js"></script>
  <script src="src/js/tank.js"></script>
  <script>
    // Initialize navigation authentication
    initializeAuthNavigation();
  </script>
</body>
</html>
