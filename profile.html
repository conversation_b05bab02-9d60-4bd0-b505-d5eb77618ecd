<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile - Your Fish Art Statistics | DrawAFish.com</title>
    <meta name="description" content="View your fish drawing statistics, total scores, and community engagement.">
    <meta name="keywords" content="user profile, fish art statistics, drawing progress, artist profile, fish drawing stats, community engagement">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="User Profile - Your Fish Art Statistics">
    <meta property="og:description" content="View your fish drawing statistics, total scores, and community engagement.">
    <meta property="og:type" content="profile">
    <meta property="og:url" content="https://drawafish.com/profile.html">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://drawafish.com/profile.html">
    
    <link rel="stylesheet" href="src/css/style.css">
    <link rel="stylesheet" href="src/css/profile.css">
    <link rel="icon" href="public/favicon.ico">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GS1GYSFBPX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-GS1GYSFBPX');
    </script>

</head>
<body>
    <div class="nav-links">
        <a href="index.html">draw</a>
        <a href="tank.html">tank</a>
        <a href="rank.html">rankings</a>
        <a href="fishtanks.html" id="my-tanks-link">my tanks</a>
        <a href="login.html" id="auth-link">login</a>
    </div>
    
    <div class="profile-container">
        <div class="profile-header">
            <h1>User Profile</h1>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            Loading profile...
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
        
        <div id="profile-empty" class="profile-empty">
            <h2>No user logged in</h2>
            <p>Please log in to view your profile</p>
            <div class="profile-actions">
                <a href="login.html" class="action-btn primary">Log In</a>
                <a href="login.html?signup=true" class="action-btn">Sign Up</a>
            </div>
        </div>
        
        <div id="profile-content" class="profile-card" style="display: none;">
            <div class="profile-info">
                <div class="profile-basic">
                    <div class="profile-avatar" id="profile-avatar"></div>
                    <div class="profile-name" id="profile-name"></div>
                    <div class="profile-email" id="profile-email"></div>
                    <div class="profile-joined" id="profile-joined"></div>
                </div>
                
                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="fish-count">0</div>
                        <div class="stat-label">Fish Created</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="total-score">0</div>
                        <div class="stat-label">Total Score</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="total-upvotes">0</div>
                        <div class="stat-label">Total Upvotes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="total-downvotes">0</div>
                        <div class="stat-label">Total Downvotes</div>
                    </div>
                </div>
            </div>
            
            <div class="profile-actions">
                <button id="edit-profile-btn" class="action-btn" onclick="toggleEditProfile()">Edit Profile</button>
                <a href="#" id="view-fish-btn" class="action-btn">View All Fish</a>
                <a href="#" id="visit-tank-btn" class="action-btn primary">Visit Tank</a>
                <button onclick="shareProfile()" class="action-btn">Share Profile</button>
            </div>
        </div>
    </div>
    
    <script src="src/js/footer-utils.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="src/js/firebase-init.js"></script>
    <script src="src/js/fish-utils.js"></script>
    <script src="src/js/profile.js"></script>
    <script>
        // Initialize navigation authentication
        initializeAuthNavigation();
    </script>
</body>
</html>