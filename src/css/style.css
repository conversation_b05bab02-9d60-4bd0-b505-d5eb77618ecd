/* Minimal functional styles */
body {
    text-align: center;
    margin: 0;
    padding: 10px;
}

/* Canvas styles */
#draw-canvas {
    border: 1px solid #000;
    background: #ffffff;
    cursor: crosshair;
}

#swim-canvas {
    display: block;
    margin: 20px auto;
    background: #4fc3f7;
    border-radius: 10px;
    border: 2px solid #0288d1;
}

/* Welcome back message styling */
#welcome-back-message {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: opacity 0.3s ease;
}

#welcome-back-message a {
    font-weight: 500;
    transition: color 0.2s ease;
}

#welcome-back-message a:hover {
    color: #004499 !important;
}

/* Fish highlighting styles for user ownership */
.fish-card.user-fish-highlight {
    background: linear-gradient(135deg, #fff9e6, #fff3d0) !important;
    border: 2px solid #ffd700 !important;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3) !important;
    transform: scale(1.02) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.fish-card.user-fish-highlight:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4) !important;
}

/* Add a subtle "Your Fish" indicator without emojis */
.fish-card.user-fish-highlight::before {
    content: 'Your Fish';
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 215, 0, 0.9);
    color: #333;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    z-index: 10;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* Test class to verify CSS is working */
.test-highlight {
    background: red !important;
    border: 5px solid blue !important;
}

/* Mobile-friendly paint bar styles */
#paint-bar {
    max-width: 100% !important;
    overflow: visible !important;
    box-sizing: border-box;
}

/* Ensure paint bar elements don't get cut off on mobile */
@media (max-width: 480px) {
    #paint-bar {
        padding: 4px 5px !important;
        margin: 4px auto !important;
        flex-direction: column !important;
        gap: 4px !important;
    }
    
    #paint-bar > div {
        width: 100%;
        justify-content: center !important;
    }
    
    /* Make canvas responsive on mobile */
    #draw-canvas {
        max-width: 100%;
        height: auto;
    }
    
    /* Smaller buttons on mobile */
    #paint-bar button {
        font-size: 11px !important;
        padding: 3px 6px !important;
        height: 22px !important;
    }
    
    /* Smaller color buttons on mobile */
    #paint-bar button[style*="border-radius: 50%"] {
        width: 20px !important;
        height: 20px !important;
        min-width: 20px !important;
        min-height: 20px !important;
    }
}
