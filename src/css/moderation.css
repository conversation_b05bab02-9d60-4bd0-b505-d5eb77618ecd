.moderation-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.moderation-header {
    background: #0288d1;
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.fish-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.fish-card {
    background: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 2px solid #e0e0e0;
}

.fish-card.reported {
    border-color: #f44336;
    background: #ffebee;
}

.fish-card.flagged {
    border-color: #ff9800;
    background: #fff3e0;
}

.fish-card.deleted {
    border-color: #757575;
    background: #f5f5f5;
    opacity: 0.7;
}

.fish-card.valid {
    border-color: #4caf50;
    background: #f1f8e9;
}

.fish-card.invalid {
    border-color: #ff9800;
    background: #fff8e1;
}

.bulk-actions {
    background: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.fish-image {
    width: 100%;
    height: 200px;
    object-fit: contain;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
}

.fish-info {
    margin: 10px 0;
    text-align: left;
}

.fish-info strong {
    color: #0288d1;
}

.moderation-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.validity-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.action-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s;
}

.delete-btn {
    background: #f44336;
    color: white;
}

.delete-btn:hover {
    background: #d32f2f;
}

.delete-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.approve-btn {
    background: #4caf50;
    color: white;
}

.approve-btn:hover {
    background: #388e3c;
}

.approve-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.reports-section {
    background: #fff3e0;
    border-radius: 5px;
    padding: 10px;
    margin-top: 10px;
}

.report-item {
    background: #fafafa;
    padding: 8px;
    margin: 5px 0;
    border-radius: 3px;
    border-left: 3px solid #ff9800;
}

.loading {
    text-align: center;
    padding: 50px;
    color: #666;
}

.stats-bar {
    display: flex;
    justify-content: space-around;
    background: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #0288d1;
}

.filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid #0288d1;
    background: white;
    color: #0288d1;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-btn.active {
    background: #0288d1;
    color: white;
}

.filter-btn:hover {
    background: #0288d1;
    color: white;
}

.logout-btn {
    background: #ff5722;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-left: auto;
}

.logout-btn:hover {
    background: #e64a19;
}