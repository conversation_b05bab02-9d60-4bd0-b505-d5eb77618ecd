/* Profile page specific styles */
.profile-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.profile-header {
    text-align: center;
    margin-bottom: 30px;
}

.profile-header h1 {
    margin: 0;
    font-size: 2.5em;
    color: #333;
}

.profile-card {
    background: white;
    border: 2px solid #ddd;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.profile-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .profile-info {
        grid-template-columns: 1fr;
    }
}

.profile-basic {
    text-align: center;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    font-weight: bold;
    margin: 0 auto 20px;
}

.profile-name {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.profile-email {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.profile-joined {
    color: #666;
    font-size: 12px;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
}

.profile-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 10px 20px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
    font-size: 14px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f0f0f0;
    transform: translateY(-1px);
}

.action-btn.primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.action-btn.primary:hover {
    background: #0056b3;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 18px;
    color: #666;
}

.error {
    text-align: center;
    padding: 40px;
    color: #dc3545;
    font-size: 18px;
}

.nav-links {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.nav-links a {
    color: #007bff;
    text-decoration: none;
    margin: 0 15px;
    font-size: 14px;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background 0.2s ease;
}

.nav-links a:hover {
    background: #e9ecef;
}

.profile-empty {
    text-align: center;
    padding: 40px;
    color: #666;
}

.profile-empty h2 {
    color: #333;
    margin-bottom: 10px;
}

/* Edit profile styles */
.edit-name-input {
    padding: 5px 10px;
    border: 2px solid #007bff;
    border-radius: 5px;
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    width: 100%;
    max-width: 300px;
    outline: none;
    transition: border-color 0.2s ease;
}

.edit-name-input:focus {
    border-color: #0056b3;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.edit-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.save-btn, .cancel-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.save-btn {
    background: #28a745;
    color: white;
}

.save-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.cancel-btn {
    background: #dc3545;
    color: white;
}

.cancel-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.saving {
    background: #6c757d !important;
    cursor: not-allowed !important;
}