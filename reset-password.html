<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Fish App</title>
    <style>
        body {
            font-family: "MS Sans Serif", sans-serif;
            font-size: 11px;
            background: #c0c0c0;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .reset-container {
            background: #c0c0c0;
            border: 2px outset #808080;
            padding: 20px;
            width: 400px;
            max-width: 90%;
        }

        .reset-header {
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .reset-form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        input[type="email"], input[type="password"] {
            padding: 2px;
            border: 1px inset #808080;
            font-family: "MS Sans Serif", sans-serif;
            font-size: 11px;
            width: 100%;
            box-sizing: border-box;
        }

        button {
            padding: 4px 12px;
            border: 1px outset #808080;
            background: #c0c0c0;
            font-family: "MS Sans Serif", sans-serif;
            font-size: 11px;
            cursor: pointer;
            margin-top: 10px;
        }

        button:active {
            border: 1px inset #808080;
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 10px 0;
        }

        .error {
            display: none;
            color: #800000;
            background: #ffcccc;
            border: 1px solid #800000;
            padding: 5px;
            margin: 10px 0;
        }

        .success {
            display: none;
            color: #008000;
            background: #ccffcc;
            border: 1px solid #008000;
            padding: 5px;
            margin: 10px 0;
        }

        .email-display {
            font-style: italic;
            margin-bottom: 15px;
            color: #000080;
        }

        .back-link {
            text-align: center;
            margin-top: 15px;
        }

        .back-link a {
            color: #000080;
            text-decoration: underline;
        }

        .invalid-token {
            text-align: center;
            color: #800000;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            Reset Your Password
        </div>

        <div id="loading" class="loading">
            Processing...
        </div>

        <div id="error" class="error">
            An error occurred. Please try again.
        </div>

        <div id="success" class="success">
            Password reset successful!
        </div>

        <!-- Valid Token Form -->
        <form id="reset-password-form" class="reset-form" style="display: none;">
            <div>
                <strong>Reset password for:</strong><br>
                <div id="email-display" class="email-display"></div>
            </div>
            
            <div>
                <strong>New Password:</strong><br>
                <input type="password" id="new-password" required minlength="6" placeholder="Enter new password">
            </div>
            
            <div>
                <strong>Confirm New Password:</strong><br>
                <input type="password" id="confirm-password" required minlength="6" placeholder="Confirm new password">
            </div>
            
            <button type="submit">Reset Password</button>
            
            <input type="hidden" id="reset-token">
            <input type="hidden" id="reset-email">
        </form>

        <!-- Invalid Token Message -->
        <div id="invalid-token" class="invalid-token" style="display: none;">
            <p><strong>Invalid or Expired Reset Link</strong></p>
            <p>This password reset link is invalid or has expired. Please request a new password reset.</p>
        </div>

        <div class="back-link">
            <a href="/login.html">Back to Login</a>
        </div>
    </div>

    <!-- Firebase and shared utilities -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="src/js/firebase-init.js"></script>
    <script src="src/js/fish-utils.js"></script>
    <script src="src/js/reset-password.js"></script>
</body>
</html>