<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Fish Tanks - Create Custom Fish Collections | DrawAFish.com</title>
    <meta name="description" content="Create and manage your own custom fish tank collections. Organize your favorite fish drawings, share tanks with friends, and discover community collections.">
    <meta name="keywords" content="custom fish tanks, fish collections, personal aquarium, organize fish art, share fish tanks, community collections">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="My Fish Tanks - Create Custom Fish Collections">
    <meta property="og:description" content="Create and manage your own custom fish tank collections. Organize your favorite fish drawings and share with friends.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://drawafish.com/fishtanks.html">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://drawafish.com/fishtanks.html">
    
    <link rel="stylesheet" href="src/css/style.css">
    <link rel="icon" href="public/favicon.ico">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GS1GYSFBPX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-GS1GYSFBPX');
    </script>
    
    <style>
        /* Main container */
        .fishtanks-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header */
        .page-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 2.5em;
            color: #333;
        }
        
        /* Navigation */
        .control-panel {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .control-panel a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
            font-weight: 500;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.2s ease;
        }
        
        .control-panel a:hover {
            background: #e9ecef;
        }
        
        /* Status bar */
        .status-bar {
            background: #e9ecef;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        /* Tab buttons */
        .tab-btn {
            padding: 10px 20px;
            border: 2px solid #333;
            background: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
            margin: 0 5px;
        }
        
        .tab-btn:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
        }
        
        .tab-btn.active {
            background: #333;
            color: white;
        }
        
        /* Tab content */
        .tab-content {
            display: none;
            margin-top: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Tank grid */
        .tank-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin: 20px 0;
            padding: 0;
        }
        
        .tank-card {
            border: 2px solid #ddd;
            border-radius: 12px;
            padding: 20px;
            background: white;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .tank-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007bff;
        }
        
        .tank-card h3 {
            margin: 0 0 12px 0;
            color: #333;
            font-size: 1.4em;
            font-weight: 600;
            line-height: 1.3;
        }
        
        .tank-info {
            margin-bottom: 16px;
            color: #666;
            line-height: 1.5;
        }
        
        .tank-info p {
            margin: 8px 0;
            font-size: 14px;
        }
        
        .tank-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.8em;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .tank-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .tank-privacy-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .tank-privacy-badge.public {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .tank-privacy-badge.private {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Buttons */
        .btn {
            padding: 10px 20px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.2s ease;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #007bff;
            color: white;
            transform: translateY(-2px);
        }
        
        .btn-small {
            padding: 5px 10px;
            margin: 2px;
            font-size: 12px;
        }
        
        /* Enhanced button styles */
        .btn-view {
            background: #007bff;
            color: white;
            border: 2px solid #007bff;
        }
        
        .btn-view:hover {
            background: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-edit {
            background: #28a745;
            color: white;
            border: 2px solid #28a745;
        }
        
        .btn-edit:hover {
            background: #1e7e34;
            border-color: #1e7e34;
            transform: translateY(-1px);
        }
        
        .btn-share {
            background: #17a2b8;
            color: white;
            border: 2px solid #17a2b8;
        }
        
        .btn-share:hover {
            background: #117a8b;
            border-color: #117a8b;
            transform: translateY(-1px);
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
            border: 2px solid #dc3545;
        }
        
        .btn-delete:hover {
            background: #c82333;
            border-color: #c82333;
            transform: translateY(-1px);
        }
        
        /* Status messages */
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .empty-state h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .loading {
            text-align: center;
            padding: 30px;
            color: #007bff;
            font-size: 16px;
        }
        
        /* Search and Filter Controls */
        .search-controls {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .search-bar {
            position: relative;
            margin-bottom: 15px;
        }
        
        .search-bar input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s ease;
            box-sizing: border-box;
        }
        
        .search-bar input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 18px;
            color: #666;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .search-btn:hover {
            background: #f8f9fa;
            color: #333;
        }
        
        .filter-controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-controls select {
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            cursor: pointer;
            transition: border-color 0.2s ease;
            min-width: 150px;
        }
        
        .filter-controls select:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .tank-count {
            margin-left: auto;
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* Modals */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            margin: 5% auto;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            animation: slideIn 0.3s ease-out;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        /* Special styling for add-to-tank modal */
        .modal-content.wide {
            max-width: 600px;
        }
        
        .close {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            color: #666;
            cursor: pointer;
            line-height: 1;
            transition: color 0.2s ease;
        }
        
        .close:hover,
        .close:focus {
            color: #333;
            text-decoration: none;
        }
        
        .share-url {
            border: 2px solid #ddd;
            padding: 15px;
            word-break: break-all;
            border-radius: 8px;
            background: #f8f9fa;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translateY(-50px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInRight {
            from { 
                opacity: 0;
                transform: translateX(100%);
            }
            to { 
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideOutRight {
            from { 
                opacity: 1;
                transform: translateX(0);
            }
            to { 
                opacity: 0;
                transform: translateX(100%);
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .fishtanks-container {
                padding: 10px;
            }
            
            .tank-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .tank-card {
                margin: 0;
            }
            
            .create-tank-form {
                margin: 10px 0;
                padding: 20px 15px;
            }
            
            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 20px 15px;
                max-height: 90vh;
            }
            
            .close {
                top: 10px;
                right: 15px;
                font-size: 24px;
            }
            
            .search-controls {
                padding: 15px;
            }
            
            .filter-controls {
                flex-direction: column;
                gap: 10px;
            }
            
            .filter-controls select {
                min-width: 100%;
            }
            
            .tank-actions {
                justify-content: center;
            }
            
            .tank-stats {
                padding: 12px;
            }
            
            .page-header h1 {
                font-size: 2em;
            }
            
            .fish-preview {
                grid-template-columns: repeat(auto-fill, minmax(45px, 1fr));
                gap: 8px;
            }
            
            .fish-item {
                width: 45px;
                height: 45px;
            }
        }
        
        @media (min-width: 769px) and (max-width: 1024px) {
            .tank-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }
        
        @media (min-width: 1025px) {
            .tank-grid {
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            }
        }
        
        /* Tank card animations */
        .tank-card {
            animation: fadeInUp 0.3s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Loading states */
        .tank-grid.loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .empty-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .empty-state p {
            margin-bottom: 20px;
            font-size: 16px;
            line-height: 1.5;
        }
        
        /* Form styles for modals */
        .modal .form-group {
            margin-bottom: 20px;
        }
        
        .modal .form-group label {
            font-weight: bold;
            display: block;
            margin-bottom: 8px;
            color: #333;
        }
        
        .modal .form-group input, 
        .modal .form-group textarea {
            width: 100%;
            border: 2px solid #ddd;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
            box-sizing: border-box;
            font-family: inherit;
        }
        
        .modal .form-group input:focus, 
        .modal .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .modal .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .modal .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .modal .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .modal .checkbox-group label {
            margin: 0;
            font-weight: normal;
            cursor: pointer;
        }
        
        /* Fish selection grid */
        .fish-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .fish-item {
            width: 50px;
            height: 50px;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        
        .fish-item:hover {
            border-color: #007bff;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }
        
        .fish-item.selected {
            border-color: #007bff;
            background: #e3f2fd;
            box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
        }
        
        .fish-item canvas {
            border-radius: 4px;
        }
        
        /* Tank selection in modal */
        .tank-selection-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            max-height: 400px;
            overflow-y: auto;
            padding: 5px;
        }
        
        .tank-selection-item {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 16px;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .tank-selection-item:hover {
            border-color: #007bff;
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }
        
        .tank-selection-item h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .tank-selection-item .tank-description {
            margin: 0 0 8px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .tank-selection-stats {
            display: flex;
            gap: 15px;
            margin: 8px 0 12px 0;
            font-size: 13px;
            color: #666;
        }
        
        .tank-selection-stats .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .tank-privacy-indicator {
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .tank-privacy-indicator.public {
            background: #d4edda;
            color: #155724;
        }
        
        .tank-privacy-indicator.private {
            background: #f8d7da;
            color: #721c24;
        }
        
        .add-to-tank-btn {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .add-to-tank-btn:hover {
            background: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
        }
        
        .no-tanks-message {
            text-align: center;
            padding: 30px 20px;
            border: 2px dashed #ddd;
            border-radius: 12px;
            background: #f8f9fa;
            color: #666;
        }
        
        .no-tanks-message h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.2em;
        }
        
        .no-tanks-message p {
            margin: 0 0 15px 0;
            line-height: 1.5;
        }
        
        .no-tanks-message .btn {
            display: inline-block;
            text-decoration: none;
        }
        
        /* Responsive design for tank selection modal */
        @media (max-width: 768px) {
            .tank-selection-grid {
                max-height: 300px;
                gap: 10px;
            }
            
            .tank-selection-item {
                padding: 12px;
            }
            
            .tank-selection-item h4 {
                font-size: 1em;
            }
            
            .tank-selection-stats {
                font-size: 12px;
                gap: 12px;
            }
            
            .add-to-tank-btn {
                padding: 8px 12px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="fishtanks-container">
        <div class="page-header">
            <h1>My Fish Tanks</h1>
        </div>
        
        <div class="control-panel">
            <strong>Navigation:</strong>
            <a href="index.html">draw</a>
            <a href="tank.html">tank</a>
            <a href="rank.html">rank</a>
            <a href="profile.html">profile</a>
            <a href="fishtanks.html" id="my-tanks-link">my tanks</a>
            <a href="login.html" id="auth-link">login</a>
        </div>

        <div class="status-bar" id="auth-info">
            <div id="auth-status">Please log in to manage your fish tanks</div>
        </div>

        <div class="control-panel">
            <button class="tab-btn active" onclick="showTab('my-tanks')">My Tanks</button>
            <button class="tab-btn" onclick="showTab('create-tank')">Create Tank</button>
            <button class="tab-btn" onclick="showTab('public-tanks')">Discover</button>
        </div>

        <!-- Search and Filter Controls -->
        <div class="search-controls" id="search-controls" style="display: none;">
            <div class="search-bar">
                <input type="text" id="tank-search" placeholder="Search tanks by name or description..." onkeyup="filterTanks()">
                <button class="search-btn" onclick="clearSearch()">✕</button>
            </div>
            <div class="filter-controls">
                <select id="tank-filter" onchange="filterTanks()">
                    <option value="all">All Tanks</option>
                    <option value="public">Public Only</option>
                    <option value="private">Private Only</option>
                </select>
                <select id="tank-sort" onchange="sortTanks()">
                    <option value="updated">Recently Updated</option>
                    <option value="created">Recently Created</option>
                    <option value="name">Name (A-Z)</option>
                    <option value="fish">Most Fish</option>
                    <option value="views">Most Views</option>
                </select>
            </div>
        </div>

        <!-- My Tanks Tab -->
        <div id="my-tanks" class="tab-content active">
            <div id="my-tanks-loading" class="loading" style="display: none;">Loading your tanks...</div>
            <div id="my-tanks-error" class="error" style="display: none;"></div>
            <div id="my-tanks-grid" class="tank-grid"></div>
            <div id="my-tanks-empty" class="empty-state" style="display: none;">
                <h3>No tanks found!</h3>
                <p>Create your first fish tank to get started.</p>
                <button class="btn" onclick="showTab('create-tank')">Create First Tank</button>
            </div>
        </div>

        <!-- Create Tank Tab -->
        <div id="create-tank" class="tab-content">
            <div class="create-tank-form">
                <h2>Create New Tank</h2>
                <div id="create-tank-error" class="error" style="display: none;"></div>
                <div id="create-tank-success" class="success" style="display: none;"></div>
                
                <form id="create-tank-form">
                    <div class="form-group">
                        <label for="tank-name">Tank Name *</label>
                        <input type="text" id="tank-name" name="name" required maxlength="50">
                    </div>
                    
                    <div class="form-group">
                        <label for="tank-description">Description</label>
                        <textarea id="tank-description" name="description" maxlength="200"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="tank-public" name="isPublic" checked>
                            Make public (others can view and add fish)
                        </label>
                    </div>
                    
                    <button type="submit" class="btn">Create Tank</button>
                </form>
            </div>
        </div>

        <!-- Public Tanks Tab -->
        <div id="public-tanks" class="tab-content">
            <div id="public-tanks-loading" class="loading" style="display: none;">Loading public tanks...</div>
            <div id="public-tanks-error" class="error" style="display: none;"></div>
            <div id="public-tanks-grid" class="tank-grid"></div>
            <div class="control-panel" id="public-tanks-pagination"></div>
        </div>
    </div>

    <!-- Edit Tank Modal -->
    <div id="edit-tank-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('edit-tank-modal')">&times;</span>
            <h2>Edit Tank</h2>
            <div id="edit-tank-error" class="error" style="display: none;"></div>
            <form id="edit-tank-form">
                <div class="form-group">
                    <label for="edit-tank-name">Tank Name *</label>
                    <input type="text" id="edit-tank-name" required maxlength="50">
                </div>
                <div class="form-group">
                    <label for="edit-tank-description">Description</label>
                    <textarea id="edit-tank-description" maxlength="200"></textarea>
                </div>
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="edit-tank-public">
                        <label for="edit-tank-public">Make this tank public</label>
                    </div>
                </div>
                <button type="submit" class="btn">Update Tank</button>
            </form>
        </div>
    </div>

    <!-- Share Tank Modal -->
    <div id="share-tank-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('share-tank-modal')">&times;</span>
            <h2>Share Tank</h2>
            <p>Share this URL to let others view your tank:</p>
            <div class="share-url" id="share-url"></div>
            <button class="btn" onclick="copyShareUrl()">Copy URL</button>
        </div>
    </div>

    <!-- Add Fish Modal -->
    <div id="add-fish-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('add-fish-modal')">&times;</span>
            <h2>Add Fish to Tank</h2>
            <div id="add-fish-error" class="error" style="display: none;"></div>
            <div id="add-fish-success" class="success" style="display: none;"></div>
            <div id="user-fish-loading" class="loading" style="display: none;">Loading available fish...</div>
            <div id="user-fish-grid" class="fish-preview"></div>
            <div id="user-fish-empty" class="empty-state" style="display: none;">
                <h3>No fish available</h3>
                <p>No fish are available to add to your tank right now.</p>
                <a href="index.html" class="btn">Draw Fish</a>
            </div>
        </div>
    </div>

    <script src="src/js/footer-utils.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="src/js/firebase-init.js"></script>
    <script src="src/js/fish-utils.js"></script>
    <script src="src/js/modal-utils.js"></script>
    <script src="src/js/fishtanks.js"></script>
    <script>
        // Initialize navigation authentication
        initializeAuthNavigation();
    </script>
</body>
</html>