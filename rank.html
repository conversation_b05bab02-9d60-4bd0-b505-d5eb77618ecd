<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Rankings - Vote on Community Fish Art | DrawAFish.com</title>
    <meta name="description" content="Vote on and rank the best fish drawings from our community. Discover trending fish art, sort by popularity, score, or date.">
    <meta name="keywords" content="fish rankings, vote fish art, community voting, fish art gallery, digital art contest, fish drawing competition">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Fish Rankings - Vote on Community Fish Art">
    <meta property="og:description" content="Vote on and rank the best fish drawings from our community. Discover trending fish art.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://drawafish.com/rank.html">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://drawafish.com/rank.html">
    
    <link rel="stylesheet" href="src/css/style.css">
    <link rel="icon" href="public/favicon.ico">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GS1GYSFBPX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-GS1GYSFBPX');
    </script>
    
    <!-- Structured Data for Rankings -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Fish Rankings - Community Art Gallery",
      "description": "Browse and vote on fish artwork from our creative community. Discover trending artists and see the most popular fish drawings.",
      "url": "https://drawafish.com/rank.html",
      "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://drawafish.com"
          },
          {
            "@type": "ListItem", 
            "position": 2,
            "name": "Rankings",
            "item": "https://drawafish.com/rank.html"
          }
        ]
      },
      "mainEntity": {
        "@type": "ItemList",
        "name": "Fish Art Rankings",
        "description": "Community-voted rankings of fish drawings",
        "numberOfItems": "500+"
      }
    }
    </script>
    
    <style>
        /* Main container */
        .ranking-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .ranking-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .ranking-header h1 {
            margin: 0;
            font-size: 2.5em;
            color: #333;
        }
        
        /* Sort controls */
        .sort-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        
        .sort-btn:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
        }
        
        .sort-btn.active {
            background: #333;
            color: white;
        }
        
        .sort-btn.active:hover {
            background: #555;
        }
        
        /* Loading state */
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #666;
        }
        
        /* Grid layout for fish ranking */
        .fish-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .fish-card {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }
        
        @media (max-width: 768px) {
            .fish-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .fish-image {
            max-width: 120px;
            max-height: 80px;
            border-radius: 5px;
        }
        
        .fish-info {
            margin-bottom: 15px;
        }
        
        .fish-artist {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .fish-artist a:hover {
            color: #007bff !important;
            text-decoration: underline !important;
        }
        
        .fish-date {
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }
        
        .fish-score {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
        }
        
        .voting-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            align-items: center;
        }
        
        .vote-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.2s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .vote-btn:hover {
            background: #f0f0f0;
            transform: translateY(-1px);
        }
        
        .upvote-btn:hover {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .downvote-btn:hover {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .report-btn {
            padding: 8px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.2s ease;
        }
        
        .report-btn:hover {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .vote-count {
            font-weight: bold;
        }
        
        /* Navigation */
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
            font-weight: 500;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.2s ease;
        }
        
        .nav-links a:hover {
            background: #e9ecef;
        }
        
        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 100px auto;
            padding: 20px;
            width: 400px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .close {
            float: right;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }
        
        .close:hover {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="nav-links">
        <a href="index.html">draw</a>
        <a href="tank.html">tank</a>
        <a href="rank.html">rankings</a>
        <a href="profile.html">profile</a>
        <a href="fishtanks.html" id="my-tanks-link">my tanks</a>
        <a href="login.html" id="auth-link">login</a>
    </div>
    
    <div class="ranking-container">
        <div class="ranking-header">
            <h1> Fish Ranking </h1>
        </div>
        
        <div class="sort-controls">
            <button class="sort-btn active" data-sort="hot">Sort by Hot</button>
            <button class="sort-btn" data-sort="score">Sort by Score</button>
            <button class="sort-btn" data-sort="date">Sort by Date</button>
            <button class="sort-btn" data-sort="random">Random Order</button>
        </div>
        
        <div id="loading" class="loading">
            Loading fish... 
        </div>
        
        <div id="fish-grid" class="fish-grid" style="display: none;">
            <!-- Fish cards will be populated here -->
        </div>
    </div>
    
    <!-- Modals are now handled by modal-utils.js -->
    
    
    <script src="src/js/footer-utils.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="src/js/firebase-init.js"></script>
    <script src="src/js/fish-utils.js"></script>
    <script src="src/js/modal-utils.js"></script>
    <script src="src/js/rank.js"></script>
    <script>
        // Modal functionality is now handled by modal-utils.js
        
        // Initialize navigation authentication
        initializeAuthNavigation();
    </script>
</body>
</html>