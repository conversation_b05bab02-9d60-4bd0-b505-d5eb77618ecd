<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Tank View</title>
    <link rel="stylesheet" href="src/css/style.css">
    <link rel="icon" href="public/favicon.ico">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-GS1GYSFBPX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-GS1GYSFBPX');
    </script>
    
    <style>
        /* Modal styling - matching rank page design */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 100px auto;
            padding: 20px;
            width: 90%;
            max-width: 500px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .close {
            float: right;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }
        
        .close:hover {
            color: #333;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #e57373;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #81c784;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #2196f3;
        }
        
        .btn-secondary {
            background: #757575;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div id="loading" style="text-align: center; padding: 20px; display: block;">Loading tank...</div>
    <div id="error" style="text-align: center; padding: 20px; color: red; display: none;"></div>

    <div id="tank-content" style="display: none;">

        <!-- Main Tank Canvas -->
        <canvas id="swim-canvas" width="800" height="400" style="border:1px solid #888; background:#e0f7fa; display:block; margin: 0 auto 15px auto;"></canvas>
        <!-- Tank Header -->
        <div style="text-align: center; margin-bottom: 20px;">
            <h1 id="tank-title" style="margin: 0; font-size: 24px;">Tank Name</h1>
            <div id="tank-description" style="color: #666; font-size: 14px; margin: 5px 0;">Tank description</div>
            <div id="tank-details" style="color: #888; font-size: 12px;">Created by • Updated</div>
        </div>

        <!-- Tank Actions -->
        <div id="tank-actions" style="text-align: center; margin-bottom: 15px;">
            <!-- Actions will be populated by JavaScript -->
        </div>

        <!-- Tank Stats -->
        <div style="text-align: center; margin-bottom: 15px; font-size: 12px; color: #666;">
            Fish: <span id="fish-count" style="font-weight: bold;">0</span>
            | Views: <span id="view-count" style="font-weight: bold;">0</span>
            | Created: <span id="created-date" style="font-weight: bold;">--</span>
            | Privacy: <span id="privacy-status" style="font-weight: bold;">--</span>
        </div>

        <!-- Fish List (Simple Grid) -->
        <div id="fish-section" style="margin-top: 30px;">
            <h3 style="text-align: center; color: #666; font-size: 16px;">Fish in this tank</h3>
            <div id="fish-loading" style="text-align: center; color: #999; display: none;">Loading fish...</div>
            <div id="fish-error" style="text-align: center; color: red; display: none;"></div>
            <div id="fish-grid" style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px; margin: 20px 0;"></div>
            <div id="fish-empty" style="text-align: center; color: #999; padding: 30px; display: none;">
                <p>This tank is empty. Add some fish to bring it to life!</p>
            </div>
        </div>
    </div>

    <!-- Add Fish Modal -->
    <div id="add-fish-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('add-fish-modal')">&times;</span>
            <h2>Add Fish to Tank</h2>
            <div id="add-fish-error" class="error" style="display: none;"></div>
            <div id="add-fish-success" class="success" style="display: none;"></div>
            <div id="user-fish-loading" style="text-align: center; color: #999; display: none;">Loading available fish...</div>
            <div id="user-fish-selection" style="display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;"></div>
            <div id="user-fish-empty" style="text-align: center; color: #999; padding: 30px; display: none;">
                <p>No fish are available to add to your tank right now.</p>
                <a href="index.html" class="btn btn-primary">Draw Fish</a>
            </div>
        </div>
    </div>

    <script src="src/js/footer-utils.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="src/js/firebase-init.js"></script>
    <script src="src/js/fish-utils.js"></script>
    <script src="src/js/modal-utils.js"></script>
    <script src="src/js/tank.js"></script>
    <script src="src/js/fishtank-view.js"></script>
    <script>
        // Initialize navigation authentication
        initializeAuthNavigation();
    </script>
</body>
</html>